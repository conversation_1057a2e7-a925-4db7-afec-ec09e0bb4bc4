import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/service/dictionary/maximum_matching_segmentation.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// A service for Chinese text segmentation and word boundary detection
class ChineseSegmentationService {
  static final ChineseSegmentationService _instance =
      ChineseSegmentationService._internal();

  /// Singleton instance
  factory ChineseSegmentationService() => _instance;

  ChineseSegmentationService._internal();

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Lock for synchronization
  final _lock = Object();

  /// Maximum matching segmentation service
  final MaximumMatchingSegmentationService _maxMatchingService =
      MaximumMatchingSegmentationService();

  /// In-memory cache for segmentation results
  final _segmentationCache = _LRUCache<String, List<List<int>>>(100);

  /// Cache for storing comprehensive segmentation data for context menu
  final _contextMenuSegmentationCache =
      _LRUCache<String, Map<String, dynamic>>(50);

  /// Completer for initialization to handle concurrent calls
  static Completer<void>? _initCompleter;

  /// Initialize the segmentation service
  Future<void> initialize() async {
    // Quick check without lock
    if (_isInitialized) return;

    // Use completer-based synchronization to handle concurrent calls
    if (_initCompleter != null) {
      // Another initialization is in progress, wait for it
      return await _initCompleter!.future;
    }

    // Create completer and start initialization
    _initCompleter = Completer<void>();

    try {
      AnxLog.info('Initializing Chinese segmentation service');

      // Double-check after acquiring the completer
      if (_isInitialized) {
        _initCompleter!.complete();
        return;
      }

      // Initialize the maximum matching service
      AnxLog.info('🔧 DEBUG: Initializing maximum matching service...');
      await _maxMatchingService.initialize();

      // Debug: Check if dictionary was loaded
      final dictionary = await _maxMatchingService.getDictionary();
      AnxLog.info(
        '🔧 DEBUG: Dictionary loaded with ${dictionary.length} words',
      );

      // Test with a simple Chinese phrase
      const testText = '我爱中国';
      final testBoundaries =
          await _maxMatchingService.getWordBoundaries(testText);
      AnxLog.info(
        '🔧 DEBUG: Test segmentation for "$testText": $testBoundaries',
      );

      _isInitialized = true;
      AnxLog.info('Chinese segmentation service initialized');
      _initCompleter!.complete();
    } catch (e) {
      AnxLog.severe('Failed to initialize Chinese segmentation service: $e');
      _initCompleter!.complete();
    } finally {
      _initCompleter = null;
    }
  }

  /// Segment Chinese text into words
  /// Returns a list of word boundaries [start, end] tuples
  ///
  /// If [bookId] is provided, the segmentation results will be cached
  Future<List<List<int>>> getWordBoundaries(String text, {int? bookId}) async {
    if (!_isInitialized) {
      await initialize();
    }

    // For very short texts, use memory cache for instant results
    if (text.length < 100) {
      final cacheKey = text;
      final cachedResult = _segmentationCache.get(cacheKey);
      if (cachedResult != null) {
        AnxLog.info(
          '✅ MEMORY CACHE HIT: Retrieved cached segmentation with ${cachedResult.length} parts',
        );
        return cachedResult;
      }
    }

    return synchronized(() async {
      try {
        final Stopwatch stopwatch = Stopwatch()..start();
        List<List<int>> boundaries;

        // If not cached or no bookId provided, perform segmentation
        final processingStopwatch = Stopwatch()..start();

        // Use compute to move segmentation to a background thread for better performance
        // Lower threshold for background processing to reduce main thread blocking
        if (text.length > 50) {
          boundaries = await compute(_isolatedSegmentation, {
            'text': text,
            'dictionary': await _maxMatchingService.getDictionary(),
          });
        } else {
          boundaries = await _maxMatchingService.getWordBoundaries(text);
        }

        final processingTime = processingStopwatch.elapsedMilliseconds;
        AnxLog.info(
          'Segmented text into ${boundaries.length} parts in ${processingTime}ms',
        );

        // Store in memory cache for faster future access
        // Increase cache threshold to reduce repeated processing
        if (text.length < 200) {
          _segmentationCache.set(text, boundaries);
        }

        final elapsed = stopwatch.elapsedMilliseconds;
        AnxLog.info(
          'Total segmentation time: ${elapsed}ms (processing: ${processingTime}ms)',
        );
        return boundaries;
      } catch (e) {
        AnxLog.severe('Error segmenting text: $e');
        // Fallback to character-by-character segmentation
        List<List<int>> result = [];
        for (int i = 0; i < text.length; i++) {
          result.add([i, i + 1]);
        }
        return result;
      }
    });
  }

  /// Static method for isolated segmentation (runs in a separate thread)
  static Future<List<List<int>>> _isolatedSegmentation(
    Map<String, dynamic> params,
  ) async {
    final text = params['text'] as String;
    final dictionary = params['dictionary'] as Map<String, bool>;

    // Simple implementation of maximum matching algorithm for isolate
    List<List<int>> boundaries = [];
    int i = 0;

    while (i < text.length) {
      int maxMatchLength = 0;

      // Try to find the longest matching word starting at position i
      for (int j = 1; j <= 4 && i + j <= text.length; j++) {
        String word = text.substring(i, i + j);
        if (dictionary.containsKey(word)) {
          maxMatchLength = j;
        }
      }

      // If no match found, use single character
      if (maxMatchLength == 0) {
        maxMatchLength = 1;
      }

      boundaries.add([i, i + maxMatchLength]);
      i += maxMatchLength;
    }

    return boundaries;
  }

  /// Test the segmentation with a sample text
  Future<List<String>> testSegmentation(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    AnxLog.info('Testing segmentation for: "$text"');

    try {
      final Stopwatch stopwatch = Stopwatch()..start();
      final segments = await _maxMatchingService.testSegmentation(text);
      final elapsed = stopwatch.elapsedMilliseconds;
      AnxLog.info(
        'Segmented words (${segments.length}) in ${elapsed}ms: ${segments.join(' | ')}',
      );
      return segments;
    } catch (e) {
      AnxLog.severe('Error testing segmentation: $e');
      return [text];
    }
  }

  /// Get the word boundary for a specific character position
  /// This helps to find the proper word that contains a specific character
  Future<List<int>> getWordBoundaryForPosition(
    String text,
    int position,
  ) async {
    // Make sure position is within range
    if (position < 0 || position >= text.length) {
      return [position, position + 1]; // Default to single character
    }

    try {
      // Get all word boundaries
      final boundaries = await getWordBoundaries(text);

      // Find the boundary that contains the position
      for (final List<int> boundary in boundaries) {
        final start = boundary[0];
        final end = boundary[1];

        if (start <= position && position < end) {
          AnxLog.info('Found word boundary $start-$end for position $position');
          return boundary;
        }
      }

      // If no matching boundary is found, return single character
      AnxLog.warning('No word boundary found for position $position in text');
      return [position, position + 1];
    } catch (e) {
      AnxLog.severe('Error getting word boundary for position $position: $e');
      return [position, position + 1]; // Fall back to single character
    }
  }

  /// Get the word boundaries that would be used for text selection
  /// Used when spaces are not visible but we still want smart selection
  Future<List<List<int>>> getSelectionBoundaries(String text) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    // For selection, we want to prioritize dictionary-based words
    // but also allow for maximum flexibility in boundary detection
    final boundaries = await getWordBoundaries(text);

    // Add single-character boundaries for any characters not covered
    final Set<int> coveredPositions = {};

    // Mark all positions covered by existing boundaries
    for (final boundary in boundaries) {
      for (int i = boundary[0]; i < boundary[1]; i++) {
        coveredPositions.add(i);
      }
    }

    // Find any positions not covered and add single-character boundaries
    final List<List<int>> allBoundaries = List.from(boundaries);
    for (int i = 0; i < text.length; i++) {
      if (!coveredPositions.contains(i)) {
        allBoundaries.add([i, i + 1]);
      }
    }

    // Sort boundaries by start position
    allBoundaries.sort((a, b) => a[0].compareTo(b[0]));

    final elapsed = stopwatch.elapsedMilliseconds;
    AnxLog.info(
      'Generated ${allBoundaries.length} selection boundaries from ${boundaries.length} word boundaries in ${elapsed}ms',
    );
    return allBoundaries;
  }

  /// Store comprehensive segmentation data for context menu use
  Future<void> storeSegmentationDataForSelection({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
    required int bookId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final Stopwatch stopwatch = Stopwatch()..start();

      // Generate comprehensive segmentation data for the context menu
      final segmentationData = await _generateComprehensiveSegmentationData(
        selectedText: selectedText,
        fullNodeText: fullNodeText,
        startOffset: startOffset,
        endOffset: endOffset,
        selectionRange: selectionRange,
      );

      // Store in cache with a unique key
      final cacheKey = '${bookId}_${startOffset}_${endOffset}_$selectedText';
      _contextMenuSegmentationCache.set(cacheKey, segmentationData);

      final elapsed = stopwatch.elapsedMilliseconds;
      AnxLog.info(
        'Stored comprehensive segmentation data for context menu in ${elapsed}ms',
      );
    } catch (e) {
      AnxLog.severe('Error storing segmentation data for selection: $e');
    }
  }

  /// Generate comprehensive segmentation data including bidirectional options
  Future<Map<String, dynamic>> _generateComprehensiveSegmentationData({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
  }) async {
    final segmentationData = <String, dynamic>{};

    try {
      // Get word boundaries for the full node text
      final allBoundaries = await getWordBoundaries(fullNodeText);

      // Find all possible word combinations that include or overlap with the selection
      final possibleWords = <Map<String, dynamic>>[];

      // Get boundaries that intersect with the selection
      for (final boundary in allBoundaries) {
        final wordStart = boundary[0];
        final wordEnd = boundary[1];

        // Check if this word boundary intersects with the selection
        if (wordStart < endOffset && wordEnd > startOffset) {
          final word = fullNodeText.substring(wordStart, wordEnd);
          possibleWords.add({
            'word': word,
            'start': wordStart,
            'end': wordEnd,
            'isExactMatch': wordStart == startOffset && wordEnd == endOffset,
            'containsSelection':
                wordStart <= startOffset && wordEnd >= endOffset,
            'overlapsSelection': true,
          });
        }
      }

      // Also get bidirectional segmentation for more options
      final leftToRightWords =
          await _maxMatchingService.testSegmentation(selectedText);
      final rightToLeftWords =
          await _maxMatchingService.testSegmentationReverse(selectedText);

      // Combine all segmentation options
      final allSegmentationOptions = <Map<String, dynamic>>[];

      // Add left-to-right segmentation
      for (final word in leftToRightWords) {
        if (word.trim().isNotEmpty) {
          allSegmentationOptions.add({
            'word': word,
            'direction': 'left-to-right',
            'type': 'segmentation',
          });
        }
      }

      // Add right-to-left segmentation
      for (final word in rightToLeftWords) {
        if (word.trim().isNotEmpty) {
          allSegmentationOptions.add({
            'word': word,
            'direction': 'right-to-left',
            'type': 'segmentation',
          });
        }
      }

      // Remove duplicates
      final uniqueWords = <String, Map<String, dynamic>>{};
      for (final option in allSegmentationOptions) {
        final word = option['word'] as String;
        if (!uniqueWords.containsKey(word)) {
          uniqueWords[word] = option;
        }
      }

      segmentationData['selectedText'] = selectedText;
      segmentationData['fullNodeText'] = fullNodeText;
      segmentationData['selectionRange'] = selectionRange;
      segmentationData['possibleWords'] = possibleWords;
      segmentationData['segmentationOptions'] = uniqueWords.values.toList();
      segmentationData['timestamp'] = DateTime.now().millisecondsSinceEpoch;

      AnxLog.info(
        'Generated ${possibleWords.length} possible words and ${uniqueWords.length} segmentation options',
      );
    } catch (e) {
      AnxLog.severe('Error generating comprehensive segmentation data: $e');
    }

    return segmentationData;
  }

  /// Get stored segmentation data for context menu
  Map<String, dynamic>? getStoredSegmentationData({
    required String selectedText,
    required int startOffset,
    required int endOffset,
    required int bookId,
  }) {
    final cacheKey = '${bookId}_${startOffset}_${endOffset}_$selectedText';
    return _contextMenuSegmentationCache.get(cacheKey);
  }

  /// Helper function for synchronized blocks
  Future<T> synchronized<T>(Future<T> Function() fn) async {
    if (!_lock.toString().contains('Mutex')) {
      // If using a simple Object for locking
      return await fn();
    } else {
      // Future implementation with proper mutex if needed
      return await fn();
    }
  }
}

/// A simple LRU cache implementation
class _LRUCache<K, V> {
  final int _capacity;
  final Map<K, V> _cache = {};
  final List<K> _keys = [];

  _LRUCache(this._capacity);

  V? get(K key) {
    if (!_cache.containsKey(key)) return null;

    // Move key to the end of the list (most recently used)
    _keys.remove(key);
    _keys.add(key);

    return _cache[key];
  }

  void set(K key, V value) {
    if (_cache.containsKey(key)) {
      // Update existing key
      _cache[key] = value;
      _keys.remove(key);
      _keys.add(key);
    } else {
      // Add new key
      _cache[key] = value;
      _keys.add(key);

      // Remove oldest key if capacity is exceeded
      if (_keys.length > _capacity) {
        final oldestKey = _keys.removeAt(0);
        _cache.remove(oldestKey);
      }
    }
  }

  void clear() {
    _cache.clear();
    _keys.clear();
  }
}
